import { test, expect } from '@playwright/test';
import { emailMock } from '../setup/email-mock';

test.describe('Simple Email Mock Test', () => {
	test.beforeEach(async ({ page }) => {
		// Setup email mocking for each test
		await emailMock.setupEmailMocking(page);
		
		// Clear previous email calls
		emailMock.clearEmailCalls();
	});

	test('should mock API calls to /api/invites', async ({ page }) => {
		// Navigate to any page
		await page.goto('/');

		// Make a direct API call to test the mocking
		const response = await page.evaluate(async () => {
			const res = await fetch('/api/invites', {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify({
					resourceType: 'organization',
					resourceId: 'test-org-id',
					role: 'member',
					inviteeEmail: '<EMAIL>',
					expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
				}),
			});
			return { status: res.status, ok: res.ok };
		});

		// Verify the API call was intercepted and returned success
		expect(response.status).toBe(200);
		expect(response.ok).toBe(true);

		// Verify email was captured
		expect(emailMock.getEmailCallCount()).toBe(1);
		
		const email = emailMock.getLastEmailCall();
		expect(email).not.toBeNull();
		expect(email!.to).toBe('<EMAIL>');
		expect(email!.subject).toContain('invited to join');
	});

	test('should handle email failure simulation', async ({ page }) => {
		// Configure mock to simulate failures
		emailMock.setEmailFailure(true);
		
		await page.goto('/');

		// Make a direct API call
		const response = await page.evaluate(async () => {
			const res = await fetch('/api/invites', {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify({
					resourceType: 'organization',
					resourceId: 'test-org-id',
					role: 'member',
					inviteeEmail: '<EMAIL>',
					expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
				}),
			});
			return { status: res.status, ok: res.ok };
		});

		// Verify the API call returned an error
		expect(response.status).toBe(500);
		expect(response.ok).toBe(false);

		// Even though it "failed", the email call was still captured for testing
		expect(emailMock.getEmailCallCount()).toBe(1);
		expect(emailMock.wasEmailSentTo('<EMAIL>')).toBe(true);

		// Reset for other tests
		emailMock.setEmailFailure(false);
	});
});

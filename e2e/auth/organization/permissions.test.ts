import { expect, test } from '@playwright/test';

test.describe('Organization Permissions E2E Tests', () => {
	// Generate unique test identifiers
	const timestamp = Date.now();
	const ownerEmail = `owner-${timestamp}@example.com`;
	const adminEmail = `admin-${timestamp}@example.com`;
	const memberEmail = `member-${timestamp}@example.com`;
	const password = 'TestPassword123';
	const orgName = `Test Org ${timestamp}`;

	// Setup: Create owner account and organization
	test.beforeAll(async ({ browser }) => {
		const page = await browser.newPage();

		// Sign up as owner
		await page.goto('/auth/signup');
		await page.fill('input[name="email"]', ownerEmail);
		await page.fill('input[name="password"]', password);
		await page.click('button[type="submit"]');

		// Wait for signup success message
		await expect(page.locator('text=User created successfully')).toBeVisible({ timeout: 10000 });

		// Sign in
		await page.goto('/auth/signin');
		await page.fill('input[name="email"]', ownerEmail);
		await page.fill('input[name="password"]', password);
		await page.click('button[type="submit"]');

		// Should be redirected to org creation since user has no orgs
		await page.waitForURL(/\/org\/new/, { timeout: 10000 });

		// Create the organization
		await page.fill('input[name="name"]', orgName);
		await page.click('button[type="submit"]');

		// Wait for redirect to clients page
		await page.waitForURL(/\/org\/.*\/clients/, { timeout: 5000 });

		await page.close();
	});

	test('owner should have full access to organization settings', async ({ page }) => {
		// Sign in as owner
		await page.goto('/auth/signin');
		await page.fill('input[name="email"]', ownerEmail);
		await page.fill('input[name="password"]', password);
		await page.click('button[type="submit"]');

		// Wait for auth to complete
		await page.waitForURL(/\//, { timeout: 5000 });

		// Navigate to organization settings
		await page.goto(`/org/${encodeURIComponent(orgName)}/settings`);

		// Check for settings only owners should see
		await expect(page.getByText(/name/i)).toBeVisible();
		await expect(page.getByText(/description/i)).toBeVisible();
		await expect(page.getByText(/save changes/i)).toBeVisible();

		// Verify organization name is displayed
		await expect(page.getByText(orgName)).toBeVisible();
	});

	test('should allow owner to invite members with different roles', async ({ page }) => {
		// Sign in as owner
		await page.goto('/auth/signin');
		await page.fill('input[name="email"]', ownerEmail);
		await page.fill('input[name="password"]', password);
		await page.click('button[type="submit"]');

		// Wait for auth to complete
		await page.waitForURL(/\//, { timeout: 5000 });

		// Navigate to member invitation page
		// This URL might be different in your application
		await page.goto(`/org/${encodeURIComponent(orgName)}/invite`);

		// Fill invitation form for admin role
		await page.fill('input[name="email"]', adminEmail);
		// Select role using the Select component - use the correct selector for the trigger
		await page.locator('[data-slot="select-trigger"]').click();
		await page.locator('[data-slot="select-item"][data-value="admin"]').click();

		// Submit the form using a more robust approach
		const submitButton = page.locator('button[type="submit"]');
		await expect(submitButton).toBeVisible();
		await submitButton.click();

		// Verify success
		await expect(page.getByText(/invitation sent|invited successfully/i)).toBeVisible({
			timeout: 5000,
		});

		// Invite a regular member
		await page.fill('input[name="email"]', memberEmail);
		// Select role using the Select component - use the correct selector for the trigger
		await page.locator('[data-slot="select-trigger"]').click();
		await page.locator('[data-slot="select-item"][data-value="member"]').click();

		// Submit the form using a more robust approach
		await expect(submitButton).toBeVisible();
		await submitButton.click();

		// Verify success
		await expect(page.getByText(/invitation sent|invited successfully/i)).toBeVisible({
			timeout: 5000,
		});

		// Navigate to members list
		await page.goto('/organization/members');

		// Verify invited members are listed
		await expect(page.getByText(adminEmail)).toBeVisible();
		await expect(page.getByText(memberEmail)).toBeVisible();
	});

	// Note: Testing actual invitation acceptance would require email integration
	// The following test simulates a user trying to access resources they don't have permission for

	test('should enforce permission boundaries between organizations', async ({ browser }) => {
		// Create a new user that doesn't belong to the test organization
		const outsiderPage = await browser.newPage();
		const outsiderEmail = `outsider-${timestamp}@example.com`;

		// Sign up as outsider
		await outsiderPage.goto('/auth/signup');
		await outsiderPage.fill('input[name="email"]', outsiderEmail);
		await outsiderPage.fill('input[name="password"]', password);
		await outsiderPage.click('button[type="submit"]');

		// Complete signup flow
		await Promise.race([
			outsiderPage.waitForURL(/\/organizations\/new/, { timeout: 5000 }),
			outsiderPage.waitForSelector('text=Welcome to ', { timeout: 5000 }),
		]);

		// If on success page, sign in
		if (outsiderPage.url().includes('/auth/')) {
			await outsiderPage.goto('/auth/signin');
			await outsiderPage.fill('input[name="email"]', outsiderEmail);
			await outsiderPage.fill('input[name="password"]', password);
			await outsiderPage.click('button[type="submit"]');
			await outsiderPage.waitForURL(/\/org\/new/, { timeout: 5000 });
		}

		// Create their own organization
		await outsiderPage.fill('input[name="name"]', `Outsider Org ${timestamp}`);
		await outsiderPage.click('button[type="submit"]');

		// Wait for redirect to dashboard/clients
		await outsiderPage.waitForURL(/\/(clients|dashboard)/, { timeout: 5000 });

		// Try to access the test organization's data
		// This assumes you have a URL structure that includes organization ID
		if (orgName) {
			await outsiderPage.goto(`/organizations/${orgName}/settings`);

			// Should be redirected to an error page or access denied
			await expect(outsiderPage.url()).not.toContain(`/organizations/${orgName}/settings`);

			// Or should see an access denied message
			const accessDenied = outsiderPage.getByText(
				/access denied|not authorized|forbidden|permission/i,
			);
			const notFound = outsiderPage.getByText(/not found|404|doesn't exist/i);

			// Either the page should not be found or access should be explicitly denied
			await expect(accessDenied.isVisible() || notFound.isVisible()).toBeTruthy();
		}

		await outsiderPage.close();
	});
});
